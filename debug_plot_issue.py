import pandas as pd
import os
import numpy as np

def debug_excel_data():
    """调试Excel数据读取和解析问题"""
    
    print("=" * 60)
    print("调试 plot_snr_results.py 图片空白问题")
    print("=" * 60)
    
    # 读取rml数据文件
    file_path = os.path.join('snr_results_tables', 'snr_results_rml.xlsx')
    
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在 {file_path}")
        return
    
    try:
        df = pd.read_excel(file_path)
        print(f"成功读取文件: {file_path}")
    except Exception as e:
        print(f"读取文件失败: {e}")
        return
    
    print("\n1. 基本信息:")
    print("-" * 30)
    print(f"数据形状: {df.shape}")
    print(f"列名: {df.columns.tolist()}")
    
    print("\n2. 前几行数据:")
    print("-" * 30)
    print(df.head())
    
    print("\n3. 数据类型:")
    print("-" * 30)
    print(df.dtypes)
    
    print("\n4. 空值检查:")
    print("-" * 30)
    print(df.isnull().sum())
    
    # 检查Model Name列
    print("\n5. Model Name列:")
    print("-" * 30)
    if 'Model Name' in df.columns:
        print(f"模型数量: {len(df['Model Name'])}")
        print(f"模型名称: {df['Model Name'].tolist()}")
    else:
        print("警告: 没有找到'Model Name'列")
        print(f"实际列名: {df.columns.tolist()}")
    
    # 检查SNR列
    snr_columns = [col for col in df.columns if col != 'Model Name']
    print(f"\n6. SNR列分析:")
    print("-" * 30)
    print(f"SNR列数量: {len(snr_columns)}")
    print(f"SNR列名: {snr_columns}")
    
    # 解析SNR值
    def parse_snr_value(snr_str):
        """解析SNR字符串为数值"""
        try:
            return float(str(snr_str).replace('dB', ''))
        except:
            return None
    
    print("\n7. SNR值解析:")
    print("-" * 30)
    snr_values = []
    for col in snr_columns:
        snr_val = parse_snr_value(col)
        print(f"列'{col}' -> 解析值: {snr_val}")
        if snr_val is not None:
            snr_values.append(snr_val)
    
    print(f"有效SNR值: {snr_values}")
    
    if not snr_values:
        print("错误: 没有找到有效的SNR值!")
        return
    
    # 检查每个模型的数据
    print("\n8. 模型数据检查:")
    print("-" * 30)
    
    for idx, row in df.iterrows():
        model_name = row.get('Model Name', f'模型_{idx}')
        print(f"\n模型: {model_name}")
        
        valid_data_count = 0
        for snr_col in snr_columns:
            acc_val = row[snr_col]
            print(f"  {snr_col}: {acc_val} (类型: {type(acc_val)})")
            
            # 检查是否为有效数据
            if pd.notna(acc_val) and acc_val != '':
                try:
                    float_val = float(acc_val)
                    valid_data_count += 1
                    print(f"    -> 有效数值: {float_val}")
                except:
                    print(f"    -> 无法转换为数值")
            else:
                print(f"    -> 空值或无效数据")
        
        print(f"  有效数据点数量: {valid_data_count}")
        
        if valid_data_count == 0:
            print(f"  警告: 模型 {model_name} 没有有效的准确率数据!")
    
    print("\n" + "=" * 60)
    print("调试完成")
    print("=" * 60)

if __name__ == '__main__':
    debug_excel_data()
